# 忘记密码接口实现说明

## 概述

根据接口文档要求，已完成三个模块的忘记密码接口框架开发：
1. 商户小程序模块 (MerchantMinaAuthApi)
2. 商户后台模块 (MerchantAdminAuthController)
3. 运营后台模块 (OperationAdminAuthController)

**注意：所有接口只实现了框架代码，具体业务逻辑需要后续补充实现。**

## 实现的接口

### 1. 商户小程序模块 (/merchant/mina)

#### 1.1 获取图形验证码
- **接口路径**: `/merchant/mina/get-verify-code`
- **请求方式**: POST
- **入参**: 无参数
- **出参**: GetVerifyCodeResult (包含验证码图片URL)

#### 1.2 校验图文验证码
- **接口路径**: `/merchant/mina/check-verify-code`
- **请求方式**: POST
- **入参**: CheckVerifyCodeParam (账号、图文验证码、平台类型)
- **出参**: CheckVerifyCodeResult (脱敏手机号、账号ID)

#### 1.3 发送短信
- **接口路径**: `/merchant/mina/sen-sms`
- **请求方式**: POST
- **入参**: SendSmsParam (账号ID、平台类型、场景值、租户ID)
- **出参**: 无

#### 1.4 校验验证码
- **接口路径**: `/merchant/mina/check-sms-code`
- **请求方式**: POST
- **入参**: CheckSmsCodeParam (账号ID、平台类型、短信验证码、场景值、租户ID)
- **出参**: 无

#### 1.5 修改密码
- **接口路径**: `/merchant/mina/update-password`
- **请求方式**: POST
- **入参**: UpdatePasswordParam (账号ID、平台类型、短信验证码、场景值、租户ID、MD5密码)
- **出参**: 无

### 2. 商户后台模块 (/merchant/web)

#### 2.1 获取图形验证码
- **接口路径**: `/merchant/web/get-verify-code`
- **请求方式**: POST
- **入参**: 无参数
- **出参**: GetVerifyCodeResult (包含验证码图片URL)

#### 2.2 校验图文验证码
- **接口路径**: `/merchant/web/check-verify-code`
- **请求方式**: POST
- **入参**: CheckVerifyCodeParam (账号、图文验证码、平台类型)
- **出参**: CheckVerifyCodeResult (脱敏手机号、账号ID)

#### 2.3 发送短信
- **接口路径**: `/merchant/web/sen-sms`
- **请求方式**: POST
- **入参**: SendSmsParam (账号ID、平台类型、场景值、租户ID)
- **出参**: 无

#### 2.4 校验验证码
- **接口路径**: `/merchant/web/check-sms-code`
- **请求方式**: POST
- **入参**: CheckSmsCodeParam (账号ID、平台类型、短信验证码、场景值、租户ID、MD5密码)
- **出参**: 无

### 3. 运营后台模块 (/operation/web)

#### 3.1 获取图形验证码
- **接口路径**: `/operation/web/get-verify-code`
- **请求方式**: POST
- **入参**: 无参数
- **出参**: GetVerifyCodeResult (包含验证码图片URL)

#### 3.2 校验图文验证码
- **接口路径**: `/operation/web/check-verify-code`
- **请求方式**: POST
- **入参**: CheckVerifyCodeParam (账号、图文验证码、平台类型)
- **出参**: CheckVerifyCodeResult (脱敏手机号、账号ID)

#### 3.3 发送短信
- **接口路径**: `/operation/web/sen-sms`
- **请求方式**: POST
- **入参**: SendSmsParam (账号ID、平台类型、场景值、租户ID)
- **出参**: 无

#### 3.4 校验验证码
- **接口路径**: `/operation/web/check-sms-code`
- **请求方式**: POST
- **入参**: CheckSmsCodeParam (账号ID、平台类型、短信验证码、场景值、租户ID)
- **出参**: 无

#### 3.5 修改密码
- **接口路径**: `/operation/web/update-password`
- **请求方式**: POST
- **入参**: UpdatePasswordParam (账号ID、平台类型、短信验证码、场景值、租户ID、MD5密码)
- **出参**: 无

## 代码特点

### 1. 框架代码特点
- 只提供接口框架，所有业务逻辑使用TODO标记
- 保持项目统一的代码风格和注解规范
- 使用Spring Boot标准注解(@RestController、@PostMapping等)
- 规范的接口路径和参数定义

### 2. Bean类设计
- 使用@Data注解自动生成getter/setter
- 实现Serializable接口
- 使用@NotBlank、@NotNull等验证注解
- 按模块分包管理

### 3. 返回值设计
- 商户小程序模块：使用CommonResult<T>包装返回值
- 商户后台模块：直接返回Result对象或void
- 运营后台模块：直接返回Result对象或void

## 文件结构

```
frt-generalgw/src/main/java/com/frt/generalgw/
├── controller/
│   ├── merchantadmin/
│   │   └── MerchantAdminAuthController.java (新增忘记密码接口)
│   ├── merchantmina/
│   │   └── MerchantMinaAuthApi.java (在现有文件中新增忘记密码接口)
│   └── operationadmin/
│       └── OperationAdminAuthController.java (新增忘记密码接口)
└── domain/
    ├── param/
    │   ├── merchantadmin/forgotpassword/ (新增)
    │   ├── merchantmina/forgotpassword/ (新增)
    │   └── operationadmin/forgotpassword/ (新增)
    └── result/
        ├── merchantadmin/forgotpassword/ (新增)
        ├── merchantmina/forgotpassword/ (新增)
        └── operationadmin/forgotpassword/ (新增)
```

## 注意事项

1. **框架代码**: 所有接口只提供框架代码，具体业务逻辑需要后续实现
2. **TODO标记**: 所有业务逻辑处都标记了TODO，便于后续开发
3. **接口路径**: 严格按照接口文档规范实现
4. **参数验证**: Bean类中已添加验证注解，但Controller层未实现具体校验逻辑
5. **返回值**:
   - 商户小程序模块使用CommonResult<T>包装
   - 其他模块直接返回Result对象或void

## 后续工作

1. **实现具体业务逻辑**: 替换所有TODO标记的代码
2. **集成中台服务调用**: 调用相应的中台服务接口
3. **添加参数校验**: 在Controller层添加具体的参数校验逻辑
4. **添加日志记录**: 按照项目规范添加日志打印
5. **异常处理**: 添加完善的异常处理机制
6. **单元测试**: 编写对应的单元测试用例

【AI打工次数+1】
