# 运营后台管理接口实现说明

## 概述

根据接口文档要求，已完成运营后台的员工管理、角色管理、菜单管理（权限列表）的接口框架开发。

**注意：所有接口只实现了框架代码，具体业务逻辑需要后续补充实现。**

## 实现的接口

### 1. 员工管理模块 (/operation/web)

#### 1.1 员工列表
- **接口路径**: `/operation/web/user-list`
- **请求方式**: POST
- **入参**: PageParam<UserListQueryParam>
  - accessToken: 访问令牌
  - searchType: 搜索类型 1-账号名称 2-账号姓名 3-账号id 4-员工手机号 5-角色id
  - searchContent: 搜索内容
  - accountStatus: 账号状态 1-正常 2-禁用 3-注销
  - page: 页码
  - pageSize: 每页数量
- **出参**: PageResult<UserListQueryResult>
  - userId: 员工ID
  - username: 账号
  - name: 姓名
  - roleTypeName: 角色名称
  - status: 账号状态
  - createTime: 创建时间
  - lastLoginTime: 最后登录时间

#### 1.2 新增员工
- **接口路径**: `/operation/web/user-add`
- **请求方式**: POST
- **入参**: UserAddParam
  - accessToken: 访问令牌
  - username: 员工账号
  - password: 密码（MD5）
  - name: 员工姓名
  - phone: 手机号
  - roleId: 角色id
  - accountStatus: 状态 1-正常 2-禁用 3-注销
- **出参**: 无

#### 1.3 修改员工
- **接口路径**: `/operation/web/user-modify`
- **请求方式**: POST
- **入参**: UserModifyParam
  - accessToken: 访问令牌
  - userId: 员工ID
  - username: 员工账号
  - password: 密码（MD5）
  - name: 员工姓名
  - phone: 手机号
  - roleId: 角色id
  - accountStatus: 状态 1-正常 2-禁用 3-注销
- **出参**: 无

#### 1.4 查询员工信息
- **接口路径**: `/operation/web/user-detail`
- **请求方式**: POST
- **入参**: UserDetailQueryParam
  - accessToken: 访问令牌
  - userId: 员工ID
- **出参**: UserDetailQueryResult
  - userId: 员工ID
  - username: 账号
  - name: 姓名
  - phone: 绑定手机号
  - roleId: 角色ID
  - roleName: 角色名称
  - accountStatus: 状态 1-正常 2-禁用 3-注销

### 2. 角色管理模块 (/operation/web)

#### 2.1 角色列表
- **接口路径**: `/operation/web/role-list`
- **请求方式**: POST
- **入参**: PageParam<RoleListQueryParam>
  - accessToken: 访问令牌
  - roleName: 角色名称
  - page: 页码
  - pageSize: 每页数量
- **出参**: PageResult<RoleListQueryResult>
  - roleId: 角色ID
  - roleName: 角色名称

#### 2.2 新增角色
- **接口路径**: `/operation/web/role-add`
- **请求方式**: POST
- **入参**: RoleAddParam
  - accessToken: 访问令牌
  - roleName: 角色名称
  - platformType: 平台类型 1-商户后台 2-商户小程序 3-运营后台
  - permissionIdList: 权限列表
- **出参**: 无

#### 2.3 修改角色
- **接口路径**: `/operation/web/role-modify`
- **请求方式**: POST
- **入参**: RoleModifyParam
  - accessToken: 访问令牌
  - roleId: 角色ID
  - roleName: 角色名称
  - platformType: 平台类型 1-商户后台 2-商户小程序 3-运营后台
  - permissionIdList: 权限列表
- **出参**: 无

#### 2.4 角色详情
- **接口路径**: `/operation/web/role-detail`
- **请求方式**: POST
- **入参**: RoleDetailQueryParam
  - accessToken: 访问令牌
  - roleId: 角色ID
- **出参**: RoleDetailQueryResult
  - roleId: 角色ID
  - roleName: 角色名称
  - remark: 角色备注
  - platformType: 平台类型 1-商户后台 2-商户小程序 3-运营后台
  - permissionIdList: 权限列表

### 3. 菜单管理模块（权限列表） (/operation/web)

#### 3.1 权限列表
- **接口路径**: `/operation/web/menu-list`
- **请求方式**: POST
- **入参**: MenuListQueryParam
  - accessToken: 访问令牌
- **出参**: List<MenuListQueryResult>
  - menu_id: 菜单id
  - parentMenuId: 父菜单id
  - menuType: 菜单类型 1-页面 2-功能
  - menuName: 菜单名称
  - menuCode: 菜单编码

## 代码特点

### 1. 框架代码特点
- 只提供接口框架，所有业务逻辑使用TODO标记
- 保持项目统一的代码风格和注解规范
- 使用Spring Boot标准注解(@RestController、@PostMapping等)
- 规范的接口路径和参数定义

### 2. 分页处理
- 使用项目统一的PageParam<T>和PageResult<T>进行分页处理
- PageParam包含query对象、page页码、pageSize每页数量
- PageResult包含records记录列表、total总条数、current当前页、size每页数量

### 3. Bean类设计
- 使用@Data注解自动生成getter/setter
- 实现Serializable接口
- 使用@NotBlank、@NotNull等验证注解
- 按模块分包管理

### 4. 返回值设计
- 列表查询接口：返回PageResult<T>分页结果
- 详情查询接口：返回对应的Result对象
- 新增/修改接口：返回void

## 文件结构

```
frt-generalgw/src/main/java/com/frt/generalgw/
├── controller/
│   └── operationadmin/
│       ├── UserManagerController.java (员工管理控制器)
│       ├── RoleManagerController.java (角色管理控制器)
│       └── MenuManagerController.java (菜单管理控制器)
└── domain/
    ├── param/
    │   └── operationadmin/
    │       ├── usermanager/ (员工管理参数)
    │       ├── rolemanager/ (角色管理参数)
    │       └── menumanager/ (菜单管理参数)
    └── result/
        └── operationadmin/
            ├── usermanager/ (员工管理结果)
            ├── rolemanager/ (角色管理结果)
            └── menumanager/ (菜单管理结果)
```

## 注意事项

1. **框架代码**: 所有接口只提供框架代码，具体业务逻辑需要后续实现
2. **TODO标记**: 所有业务逻辑处都标记了TODO，便于后续开发
3. **接口路径**: 严格按照接口文档规范实现
4. **参数验证**: Bean类中已添加验证注解，但Controller层未实现具体校验逻辑
5. **分页处理**: 使用项目统一的PageParam和PageResult进行分页处理
6. **返回值**: 
   - 分页查询返回PageResult<T>
   - 详情查询返回对应Result对象
   - 新增/修改操作返回void

## 后续工作

1. **实现具体业务逻辑**: 替换所有TODO标记的代码
2. **集成中台服务调用**: 调用相应的中台服务接口
3. **添加参数校验**: 在Controller层添加具体的参数校验逻辑
4. **添加日志记录**: 按照项目规范添加日志打印
5. **异常处理**: 添加完善的异常处理机制
6. **单元测试**: 编写对应的单元测试用例

【AI打工次数+1】
