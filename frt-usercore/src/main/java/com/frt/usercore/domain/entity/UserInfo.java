/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.manager.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工信息实体类
 *
 * <AUTHOR>
 * @version UserInfo.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String phone;

    /**
     * 员工邮箱
     */
    private String email;

    /**
     * 员工状态：Y-启用，N-禁用
     */
    private String status;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}