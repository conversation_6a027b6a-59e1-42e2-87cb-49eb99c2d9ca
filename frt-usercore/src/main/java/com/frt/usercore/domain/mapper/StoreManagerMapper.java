package com.frt.usercore.manager.domain.mapper;

import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.manager.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.manager.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.manager.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.manager.domain.result.storemanager.StoreListQueryResult;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface StoreManagerMapper {

    StoreListQueryResult coverMerchantStoreInfoDOToStoreListQueryResult(MerchantStoreInfoDO infoDO);

    MerchantStoreInfoDO coverStoreInfoAddParamToStoreInfoDO(StoreInfoAddParam param);

    MerchantStoreInfoDO coverStoreInfoUpdateParamToStoreInfoDO(StoreInfoUpdateParam param);

    StoreInfoQueryResult coverStoreInfoUpdateParamToStoreInfoQueryResult(MerchantStoreInfoDO infoDO);
}
